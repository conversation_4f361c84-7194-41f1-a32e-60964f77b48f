# Imbricate ▦

```
┌───────┐
│ I M B │
│ R I C │
│ A T E │
└───────┘
```

[![Continuous Integration](https://github.com/Imbricate/Imbricate/actions/workflows/ci.yml/badge.svg)](https://github.com/Imbricate/Imbricate/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/Imbricate/Imbricate/branch/main/graph/badge.svg)](https://codecov.io/gh/Imbricate/Imbricate)
[![npm version](https://badge.fury.io/js/%40imbricate%2Fcore.svg)](https://badge.fury.io/js/%40imbricate%2Fcore)
[![downloads](https://img.shields.io/npm/dm/@imbricate/core.svg)](https://www.npmjs.com/package/@imbricate/core)

Yes to accountable notes, no to external hosting. Free, for everyone.

## Install

```sh
yarn add @imbricate/core
# Or
npm install @imbricate/core --save
```

## Documentation

Visit [imbricate.io](https://imbricate.io/).

## Quick Start with Imbricate VSCode Extension

Imbricate VSCode Extension is available through the Visual Studio Code Marketplace. See [Imbricate VSCode Extension](https://marketplace.visualstudio.com/items?itemName=imbricate.imbricate).

## Quick Start with Imbricate CLI

Imbricate CLI is available through NPM for package management. See https://github.com/imbricate/Imbricate-CLI.

To install Imbricate CLI:

### Using Yarn

Yarn 1.x

```sh
yarn global add imbricate
```

Yarn 2.x

```sh
yarn dlx imbricate
```

### Using NPM

```sh
npm install -g imbricate
```

## Usage

```sh
imbricate --help
```

```
┌─────────────────────┐
│ I M B R I C A T E ▦ │
└─────────────────────┘
```
