/**
 * <AUTHOR>
 * @namespace Error
 * @description Export
 */

export * from "./database-manager/database-manager-error";
export * from "./database-manager/feature-not-supported";
export * from "./database/database-error";
export * from "./database/feature-not-supported";
export * from "./document/document-error";
export * from "./document/feature-not-supported";
export * from "./imbricate-error";
export * from "./origin/feature-not-supported";
export * from "./origin/origin-error";
export * from "./static-manager/feature-not-supported";
export * from "./static-manager/static-manager-error";
export * from "./static/feature-not-supported";
export * from "./static/static-error";
export * from "./text-manager/feature-not-supported";
export * from "./text-manager/text-manager-error";
export * from "./text/feature-not-supported";
export * from "./text/text-error";

