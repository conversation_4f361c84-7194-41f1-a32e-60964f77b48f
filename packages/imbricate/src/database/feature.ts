/**
 * <AUTHOR>
 * @namespace Database
 * @description Feature
 */

export enum IMBRICATE_DATABASE_FEATURE {

    DATABASE_PUT_SCHEMA = "DATABASE_PUT_SCHEMA",

    DATABASE_CREATE_DOCUMENT = "DATABASE_CREATE_DOCUMENT",
    DATABASE_DELETE_DOCUMENT = "DATABASE_DELETE_DOCUMENT",

    DATABASE_GET_DOCUMENT = "DATABASE_GET_DOCUMENT",

    DATABASE_PUT_ANNOTATION = "DATABASE_PUT_ANNOTATION",
    DATABASE_DELETE_ANNOTATION = "DATABASE_DELETE_ANNOTATION",

    DATABASE_PUT_EDIT_RECORD = "DATABASE_PUT_EDIT_RECORD",
    DATABASE_GET_EDIT_RECORD = "DATABASE_GET_EDIT_RECORD",

    DATABASE_GET_ORIGIN_ACTIONS = "DATABASE_GET_ORIGIN_ACTIONS",
    DATABASE_EXECUTE_ORIGIN_ACTION = "DATABASE_EXECUTE_ORIGIN_ACTION",
}

export const checkImbricateDatabaseFeatureSupported = (
    features: IMBRICATE_DATABASE_FEATURE[],
    feature: IMBRICATE_DATABASE_FEATURE,
): boolean => {

    return features.includes(feature);
};
