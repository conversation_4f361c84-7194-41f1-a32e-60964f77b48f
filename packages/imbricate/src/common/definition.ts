/**
 * <AUTHOR>
 * @namespace Common
 * @description Definition
 */

export enum IMBRICATE_QUERY_COMPARE_CONDITION {

    EQUAL = "EQUAL",
    EXIST = "EXIST",
    GREATER_THAN = "GREATER_THAN",
    LESS_THAN = "LESS_THAN",
    GREATER_THAN_OR_EQUAL = "GREATER_THAN_OR_EQUAL",
    LESS_THAN_OR_EQUAL = "LESS_THAN_OR_EQUAL",
}

export enum IMBRICATE_QUERY_ATTRIBUTE {

    KEY = "KEY",
    VALUE = "VALUE",
}

export enum IMBRICATE_QUERY_PROPERTY_CONDITION_TARGET {

    PROPERTY_TYPE = "PROPERTY_TYPE",
    PROPERTY_VALUE = "PROPERTY_VALUE",
}
