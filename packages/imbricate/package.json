{"name": "@imbricate/core", "version": "3.32.0", "description": "Imbricate Core, Notebook for Engineers", "scripts": {"clean": "rm -rf app", "test": "jest", "coverage": "jest --coverage", "lint": "eslint src test", "build": "tsc --project typescript/tsconfig.build.json", "compile": "tsc --project typescript/tsconfig.compile.json", "license": "sdb license", "release": "pnpm lint && pnpm test && pnpm license && pnpm build", "publish-dry-run": "pnpm release && sdb --only publish-dry-run", "publish": "pnpm release && sdb --only publish"}, "repository": {"type": "git", "url": "git+https://github.com/Imbricate/Imbricate.git"}, "keywords": ["Notebook", "Engineer", "API"], "author": "WMXPY", "license": "MIT", "bugs": {"url": "https://github.com/Imbricate/Imbricate/issues"}, "homepage": "https://imbricate.io", "devDependencies": {"@imbricate/eslint-config": "workspace:*", "eslint": "9.33.0"}, "optionalDependencies": {"@sudoo/locale": "2.0.0"}}