name: Continuous Integration

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
        node-version:
          - 22
          - 24
        architecture:
          - x64
          - arm64
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false
      - name: Use Node.js ${{ matrix.node-version }} on ${{ matrix.os }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          architecture: ${{ matrix.architecture }}
          cache: pnpm
      - name: Install Dependencies
        run: |
          pnpm install
      - name: Lint
        run: |
          pnpm lint
      - name: Build
        run: |
          pnpm build
      - name: Test
        run: |
          pnpm coverage
      # - name: Codecov
      #   uses: codecov/codecov-action@v4
      #   with:
      #     token: ${{ secrets.CODECOV_TOKEN }}
